
import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Upload,
  Link,
  Maximize,
  SkipBack,
  SkipForward,
  Crown,
  Users
} from "lucide-react";
import { extractYouTubeVideoId, isYouTubeUrl, getYouTubeEmbedUrl } from "@/utils/youtube";
import { useRoomVideoState } from "@/hooks/useRoomVideoState";
import { supabase, createAuthenticatedSupabaseClient } from "@/lib/supabase";
import { toast } from "sonner";
import { useAuth } from "@clerk/clerk-react";
import { ensureVideosBucket } from "@/utils/storage-setup";
import { validateVideoFile, formatFileSize, getEstimatedUploadTime } from "@/utils/file-validation";
import { testStorageAccess } from "@/utils/storage-test";

interface VideoPlayerProps {
  roomId: string;
  isMuted: boolean;
  onMuteToggle: () => void;
  isOwner: boolean;
  onPlayingStateChange?: (isPlaying: boolean) => void;
}

// Declare YouTube API types
declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

const VideoPlayer = ({ roomId, isMuted, onMuteToggle, isOwner, onPlayingStateChange }: VideoPlayerProps) => {
  const { videoState, loading, setVideoUrl, setPlaying, setCurrentTime, setDuration } = useRoomVideoState(roomId, isOwner);
  const { getToken, isSignedIn } = useAuth();
  const [inputUrl, setInputUrl] = useState("");
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [volume, setVolume] = useState(1);
  const [youTubeApiStatus, setYouTubeApiStatus] = useState<'loading' | 'loaded' | 'failed' | 'idle'>('idle');
  const [youtubePlayerReady, setYoutubePlayerReady] = useState(false);
  const [syncingVideo, setSyncingVideo] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const youTubePlayerRef = useRef<any>(null);
  const youTubeContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Derived state from videoState
  const hasVideo = !!videoState?.video_url;
  const isPlaying = videoState?.is_playing || false;
  const currentTime = videoState?.video_current_time || 0;
  const duration = videoState?.video_duration || 0;
  const isYouTubeVideo = videoState?.video_type === 'youtube';
  const youTubeVideoId = videoState?.youtube_video_id;

  // Notify parent component of playing state changes
  useEffect(() => {
    onPlayingStateChange?.(isPlaying);
  }, [isPlaying, onPlayingStateChange]);

  // Load YouTube API when needed
  useEffect(() => {
    if (isYouTubeVideo && youTubeVideoId && youTubeApiStatus === 'idle') {
      setYouTubeApiStatus('loading');

      // Load YouTube API script
      if (!window.YT) {
        const script = document.createElement('script');
        script.src = 'https://www.youtube.com/iframe_api';
        script.async = true;

        // Add error handling for script loading
        script.onload = () => {
          console.log('YouTube API script loaded successfully');
        };

        script.onerror = () => {
          console.error('Failed to load YouTube API script');
          setYouTubeApiStatus('failed');
        };

        document.body.appendChild(script);

        // Set up API ready callback with timeout
        window.onYouTubeIframeAPIReady = () => {
          console.log('YouTube API ready');
          setYouTubeApiStatus('loaded');
        };

        // Fallback timeout in case API doesn't load
        const timeoutId = setTimeout(() => {
          setYouTubeApiStatus(current => {
            if (current === 'loading') {
              console.error('YouTube API loading timeout');
              return 'failed';
            }
            return current;
          });
        }, 10000); // 10 second timeout

        // Clear timeout if component unmounts
        return () => clearTimeout(timeoutId);
      } else if (window.YT && window.YT.Player) {
        setYouTubeApiStatus('loaded');
      } else {
        // YT object exists but not fully loaded
        setTimeout(() => {
          if (window.YT && window.YT.Player) {
            setYouTubeApiStatus('loaded');
          } else {
            setYouTubeApiStatus('failed');
          }
        }, 1000);
      }
    }
  }, [isYouTubeVideo, youTubeVideoId, youTubeApiStatus]);

  // Initialize YouTube player when API is loaded
  useEffect(() => {
    if (isYouTubeVideo && youTubeVideoId && youTubeApiStatus === 'loaded' && youTubeContainerRef.current && !youTubePlayerRef.current) {
      try {
        console.log('Initializing YouTube player for video:', youTubeVideoId);

        youTubePlayerRef.current = new window.YT.Player(youTubeContainerRef.current, {
          videoId: youTubeVideoId,
          playerVars: {
            autoplay: 0,
            controls: isOwner ? 1 : 0, // Only show controls for owner
            disablekb: !isOwner ? 1 : 0, // Disable keyboard controls for non-owners
            fs: 1,
            modestbranding: 1,
            rel: 0,
            showinfo: 0,
            origin: window.location.origin // Add origin for security
          },
          events: {
            onReady: (event: any) => {
              console.log('YouTube player ready');
              setYoutubePlayerReady(true);
              if (isOwner) {
                // Set initial volume
                event.target.setVolume(isMuted ? 0 : volume * 100);
              }
            },
            onStateChange: (event: any) => {
              if (isOwner) {
                // Owner controls sync to room state
                const playerState = event.data;
                const isPlaying = playerState === window.YT.PlayerState.PLAYING;
                const currentTime = event.target.getCurrentTime();
                const duration = event.target.getDuration();

                console.log('YouTube state change:', { playerState, isPlaying, currentTime, duration });
                setPlaying(isPlaying);
                setCurrentTime(currentTime);
                setDuration(duration);
              }
            },
            onError: (event: any) => {
              console.error('YouTube player error:', event.data);
              const errorMessages: { [key: number]: string } = {
                2: 'Invalid video ID',
                5: 'HTML5 player error',
                100: 'Video not found or private',
                101: 'Video not available in embedded players',
                150: 'Video not available in embedded players'
              };
              const message = errorMessages[event.data] || 'Unknown YouTube error';
              toast.error(`YouTube Error: ${message}`);
              setYouTubeApiStatus('failed');
            }
          }
        });
      } catch (error) {
        console.error('Error initializing YouTube player:', error);
        toast.error('Failed to initialize YouTube player');
        setYouTubeApiStatus('failed');
      }
    }
  }, [isYouTubeVideo, youTubeVideoId, youTubeApiStatus, isOwner, isMuted, volume, setPlaying, setCurrentTime, setDuration]);

  // Sync video playback with room state
  useEffect(() => {
    if (!syncingVideo) {
      if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
        // Sync YouTube player (for non-owners only to avoid conflicts)
        if (!isOwner) {
          setSyncingVideo(true);
          try {
            if (isPlaying) {
              youTubePlayerRef.current.playVideo();
            } else {
              youTubePlayerRef.current.pauseVideo();
            }
          } catch (error) {
            console.error('Error syncing YouTube playback:', error);
          }
          setTimeout(() => setSyncingVideo(false), 1000);
        }
      } else if (!isYouTubeVideo && videoRef.current) {
        // Sync regular video for both owners and non-owners
        setSyncingVideo(true);
        try {
          if (isPlaying) {
            videoRef.current.play().catch(error => {
              console.error('Error playing video:', error);
            });
          } else {
            videoRef.current.pause();
          }
        } catch (error) {
          console.error('Error syncing video playback:', error);
        }
        setTimeout(() => setSyncingVideo(false), 500);
      }
    }
  }, [isPlaying, isYouTubeVideo, youtubePlayerReady, isOwner, syncingVideo]);

  // Sync video time with room state
  useEffect(() => {
    if (!isOwner && !syncingVideo) { // Only sync for non-owners
      if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
        // Sync YouTube player time
        setSyncingVideo(true);
        try {
          const playerTime = youTubePlayerRef.current.getCurrentTime();
          if (Math.abs(playerTime - currentTime) > 2) { // Only sync if difference > 2 seconds
            youTubePlayerRef.current.seekTo(currentTime, true);
          }
        } catch (error) {
          console.error('Error syncing YouTube time:', error);
        }
        setTimeout(() => setSyncingVideo(false), 1000);
      } else if (!isYouTubeVideo && videoRef.current) {
        // Sync regular video time
        try {
          const playerTime = videoRef.current.currentTime;
          if (Math.abs(playerTime - currentTime) > 2) { // Only sync if difference > 2 seconds
            videoRef.current.currentTime = currentTime;
          }
        } catch (error) {
          console.error('Error syncing video time:', error);
        }
      }
    }
  }, [currentTime, isYouTubeVideo, youtubePlayerReady, isOwner, syncingVideo]);

  // Cleanup YouTube player when video changes or component unmounts
  useEffect(() => {
    return () => {
      if (youTubePlayerRef.current) {
        try {
          youTubePlayerRef.current.destroy();
        } catch (error) {
          console.error('Error destroying YouTube player:', error);
        }
        youTubePlayerRef.current = null;
        setYoutubePlayerReady(false);
      }
    };
  }, [youTubeVideoId]);

  // Reset YouTube player state when video changes
  useEffect(() => {
    if (youTubePlayerRef.current && !isYouTubeVideo) {
      try {
        youTubePlayerRef.current.destroy();
      } catch (error) {
        console.error('Error destroying YouTube player:', error);
      }
      youTubePlayerRef.current = null;
      setYoutubePlayerReady(false);
      setYouTubeApiStatus('idle');
    }
  }, [isYouTubeVideo]);

  useEffect(() => {
    if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
      youTubePlayerRef.current.setVolume(isMuted ? 0 : volume * 100);
    } else if (videoRef.current) {
      videoRef.current.muted = isMuted;
    }
  }, [isMuted, isYouTubeVideo, youtubePlayerReady, volume]);

  // Ensure video element is properly initialized when video URL changes
  useEffect(() => {
    if (!isYouTubeVideo && videoRef.current && videoState?.video_url) {
      const video = videoRef.current;

      const handleCanPlay = () => {
        console.log('Video can play, syncing state');
        // Sync the video element with the room state
        if (isOwner) {
          // For owners, ensure the video starts playing if room state says it should
          if (isPlaying) {
            video.play().catch(error => {
              console.error('Error auto-playing video for owner:', error);
            });
          }
        } else {
          // For non-owners, sync to room state
          video.currentTime = currentTime;
          if (isPlaying) {
            video.play().catch(error => {
              console.error('Error syncing video playback:', error);
            });
          }
        }
      };

      video.addEventListener('canplay', handleCanPlay);

      return () => {
        video.removeEventListener('canplay', handleCanPlay);
      };
    }
  }, [videoState?.video_url, isYouTubeVideo, isOwner, isPlaying, currentTime]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !isOwner) {
      return;
    }

    // Validate the file before starting upload
    const validation = validateVideoFile(file);
    if (!validation.isValid) {
      toast.error(validation.error || 'Invalid file selected');
      // Reset the file input
      if (event.target) {
        event.target.value = '';
      }
      return;
    }

    // Show warnings if any
    if (validation.warnings) {
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });
    }

    // Show upload info for large files
    if (file.size > 52428800) { // 50MB
      toast.info(`Uploading ${formatFileSize(file.size)} - estimated time: ${getEstimatedUploadTime(file.size)}`);
    }

    setUploadingFile(true);

    try {
      // Ensure the videos bucket exists (this should always return true now)
      await ensureVideosBucket();

      // Get authenticated Supabase client
      let client = supabase;
      if (isSignedIn) {
        try {
          const token = await getToken({ template: 'supabase' });
          if (token) {
            client = createAuthenticatedSupabaseClient(token);
          }
        } catch (authError) {
          console.warn('Could not get authenticated client, using anonymous:', authError);
        }
      }

      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${roomId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      // Upload file to Supabase Storage
      console.log(`Attempting to upload file: ${fileName}, size: ${formatFileSize(file.size)}, type: ${file.type}`);

      const { data, error } = await client.storage
        .from('videos')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error details:', {
          message: error.message,
          statusCode: (error as any).statusCode,
          error: error
        });

        // Provide specific error messages based on error type
        if (error.message?.includes('exceeded the maximum allowed size')) {
          throw new Error(`File size exceeds the maximum limit of 500MB. Your file is ${formatFileSize(file.size)}.`);
        } else if (error.message?.includes('Invalid MIME type')) {
          throw new Error('Unsupported video format. Please use MP4, WebM, or other supported formats.');
        } else if (error.message?.includes('Bucket not found')) {
          throw new Error('Video storage is not properly configured. Please contact support.');
        } else if (error.message?.includes('Insufficient permissions')) {
          throw new Error('You do not have permission to upload videos to this room.');
        } else {
          throw new Error(`Upload failed: ${error.message}`);
        }
      }

      console.log('Upload successful:', data);

      // Get the public URL
      const { data: { publicUrl } } = client.storage
        .from('videos')
        .getPublicUrl(fileName);

      // Update the video state with the public URL
      setVideoUrl(publicUrl, 'file');
      toast.success(`Video uploaded successfully! (${formatFileSize(file.size)})`);

    } catch (error) {
      console.error('Error uploading video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload video. Please try again.';
      toast.error(errorMessage);
    } finally {
      setUploadingFile(false);
      // Reset the file input
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  const handleUrlSubmit = () => {
    if (inputUrl.trim() && isOwner) {
      const trimmedUrl = inputUrl.trim();

      // Check if it's a YouTube URL
      if (isYouTubeUrl(trimmedUrl)) {
        const videoId = extractYouTubeVideoId(trimmedUrl);
        if (videoId) {
          console.log('Setting YouTube video:', { url: trimmedUrl, videoId });
          setVideoUrl(trimmedUrl, 'youtube', videoId);
          setShowUrlInput(false);
          setInputUrl("");
          toast.success('YouTube video added successfully');
          return;
        } else {
          toast.error('Invalid YouTube URL. Please check the URL and try again.');
          return;
        }
      }

      // Validate regular video URLs
      try {
        new URL(trimmedUrl);
        setVideoUrl(trimmedUrl, 'url');
        setShowUrlInput(false);
        setInputUrl("");
        toast.success('Video URL added successfully');
      } catch (error) {
        toast.error('Invalid URL format. Please enter a valid video URL.');
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current && isOwner) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && isOwner) {
      setDuration(videoRef.current.duration);
      // Auto-play the video when it's first loaded (for uploaded videos)
      if (videoState?.video_type === 'file' && !isPlaying) {
        setPlaying(true);
      }
    }
  };

  const handleSeek = (time: number) => {
    if (isOwner) {
      try {
        if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
          youTubePlayerRef.current.seekTo(time, true);
          setCurrentTime(time);
        } else if (!isYouTubeVideo && videoRef.current) {
          videoRef.current.currentTime = time;
          setCurrentTime(time);
        }
      } catch (error) {
        console.error('Error seeking video:', error);
      }
    }
  };

  const handlePlayPause = () => {
    if (isOwner) {
      if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
        // YouTube player will trigger state change event which updates room state
        if (isPlaying) {
          youTubePlayerRef.current.pauseVideo();
        } else {
          youTubePlayerRef.current.playVideo();
        }
      } else if (!isYouTubeVideo && videoRef.current) {
        // For regular videos, control both the video element and room state
        const newPlayingState = !isPlaying;
        setPlaying(newPlayingState);

        // Immediately control the video element
        if (newPlayingState) {
          videoRef.current.play().catch(error => {
            console.error('Error playing video:', error);
          });
        } else {
          videoRef.current.pause();
        }
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    try {
      if (isYouTubeVideo && youTubePlayerRef.current && youtubePlayerReady) {
        youTubePlayerRef.current.setVolume(isMuted ? 0 : newVolume * 100);
      } else if (videoRef.current) {
        videoRef.current.volume = newVolume;
      }
    } catch (error) {
      console.error('Error changing volume:', error);
    }
  };

  if (!hasVideo) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-slate-900">
        <Card className="w-full max-w-md mx-4 bg-slate-800 border-slate-700">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <Play className="w-8 h-8 text-slate-300" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No Video Loaded</h3>
            <p className="text-slate-400 mb-6">Upload a video file or add a YouTube URL to start watching together.</p>
            
            {isOwner ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadingFile}
                    className="w-full bg-slate-700 hover:bg-slate-600 text-white disabled:opacity-50"
                  >
                    {uploadingFile ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Video File
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-slate-400 text-center">
                    Max file size: 500MB • Supported: MP4, WebM, OGG, AVI, MOV
                  </p>

                  {/* Debug button for development */}
                  {process.env.NODE_ENV === 'development' && (
                    <Button
                      onClick={() => testStorageAccess(getToken, isSignedIn)}
                      variant="outline"
                      size="sm"
                      className="w-full text-xs"
                    >
                      🔧 Test Storage Access
                    </Button>
                  )}
                </div>
                
                <Button 
                  onClick={() => setShowUrlInput(!showUrlInput)}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Link className="w-4 h-4 mr-2" />
                  Add Video URL
                </Button>

                {showUrlInput && (
                  <div className="flex gap-2 mt-4">
                    <Input
                      placeholder="Enter YouTube URL or video URL..."
                      value={inputUrl}
                      onChange={(e) => setInputUrl(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                    <Button onClick={handleUrlSubmit} size="sm">
                      Add
                    </Button>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            ) : (
              <p className="text-slate-500 text-sm">Only the room owner can add videos.</p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // YouTube Player Component with API integration
  const YouTubePlayer = () => {
    if (!youTubeVideoId) return null;

    if (youTubeApiStatus === 'loading') {
      return (
        <div className="w-full h-full flex items-center justify-center bg-slate-900">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-white text-sm">Loading YouTube Player...</p>
          </div>
        </div>
      );
    }

    if (youTubeApiStatus === 'failed') {
      return (
        <div className="w-full h-full flex items-center justify-center bg-slate-900">
          <div className="text-center">
            <p className="text-red-400 text-sm mb-2">Failed to load YouTube video</p>
            <p className="text-slate-400 text-xs mb-4">The video may be private, restricted, or unavailable</p>
            {isOwner && (
              <Button
                onClick={() => {
                  setYouTubeApiStatus('idle');
                  setYoutubePlayerReady(false);
                  if (youTubePlayerRef.current) {
                    try {
                      youTubePlayerRef.current.destroy();
                    } catch (error) {
                      console.error('Error destroying player during retry:', error);
                    }
                    youTubePlayerRef.current = null;
                  }
                }}
                variant="outline"
                size="sm"
                className="text-white border-slate-600 hover:bg-slate-700"
              >
                Try Again
              </Button>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full h-full">
        <div ref={youTubeContainerRef} className="w-full h-full" />

        {/* Control Status Overlay */}
        <div className="absolute top-2 right-2 flex gap-2">
          <Badge variant={isOwner ? "default" : "secondary"} className="bg-black bg-opacity-70">
            {isOwner ? (
              <>
                <Crown className="w-3 h-3 mr-1" />
                Owner (Controls)
              </>
            ) : (
              <>
                <Users className="w-3 h-3 mr-1" />
                Viewer
              </>
            )}
          </Badge>
          {syncingVideo && (
            <Badge variant="outline" className="bg-blue-600 bg-opacity-70 text-white border-blue-400">
              Syncing...
            </Badge>
          )}
        </div>

        {!isOwner && (
          <div className="absolute bottom-2 left-2 bg-amber-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
            <span>Only the room owner can control playback</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative w-full h-full bg-black group">
      {isYouTubeVideo ? (
        <YouTubePlayer />
      ) : (
        <>
          <video
            ref={videoRef}
            src={videoState?.video_url}
            className="w-full h-full object-contain"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            controls={false}
          />

          {/* Control Status Overlay for Regular Videos */}
          <div className="absolute top-2 right-2 flex gap-2">
            <Badge variant={isOwner ? "default" : "secondary"} className="bg-black bg-opacity-70">
              {isOwner ? (
                <>
                  <Crown className="w-3 h-3 mr-1" />
                  Owner (Controls)
                </>
              ) : (
                <>
                  <Users className="w-3 h-3 mr-1" />
                  Viewer
                </>
              )}
            </Badge>
          </div>

          {!isOwner && (
            <div className="absolute bottom-2 left-2 bg-amber-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
              <span>Only the room owner can control playback</span>
            </div>
          )}

          {/* Video Controls Overlay - Only for non-YouTube videos */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="absolute bottom-0 left-0 right-0 p-6">
              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center gap-2 text-white text-sm mb-2">
                  <span>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span>{formatTime(duration)}</span>
                </div>
                <div 
                  className="w-full h-2 bg-slate-700 rounded-full cursor-pointer"
                  onClick={(e) => {
                    if (isOwner && duration > 0) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const percent = (e.clientX - rect.left) / rect.width;
                      handleSeek(percent * duration);
                    }
                  }}
                >
                  <div 
                    className="h-full bg-white rounded-full transition-all duration-150"
                    style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                  />
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {isOwner && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipBack className="w-5 h-5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePlayPause}
                        className="text-white hover:bg-white/20"
                      >
                        {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipForward className="w-5 h-5" />
                      </Button>
                    </>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMuteToggle}
                      className="text-white hover:bg-white/20"
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </Button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                      className="w-20 h-1 bg-slate-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    videoRef.current?.requestFullscreen();
                  }}
                  className="text-white hover:bg-white/20"
                >
                  <Maximize className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VideoPlayer;
